using Microsoft.IdentityModel.Tokens;
using System.Collections.Concurrent;
using System.Text.Json;

namespace AssistantService.Services;

/// <summary>
/// Background service that periodically refreshes JWKS cache to avoid synchronous calls in JWT validation
/// </summary>
public class JwksBackgroundService : BackgroundService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<JwksBackgroundService> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ConcurrentDictionary<string, SecurityKey> _keyCache;
    private readonly TimeSpan _refreshInterval = TimeSpan.FromMinutes(30); // Refresh every 30 minutes
    private readonly TimeSpan _initialDelay = TimeSpan.FromSeconds(5); // Initial delay before first refresh

    public JwksBackgroundService(
        IConfiguration configuration,
        ILogger<JwksBackgroundService> logger,
        IHttpClientFactory httpClientFactory,
        JwksKeyCache keyCache)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _keyCache = keyCache.Keys;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("JWKS Background Service starting for Assistant service");

        // Initial delay to allow the application to start up
        await Task.Delay(_initialDelay, stoppingToken);

        // Perform initial cache population
        await RefreshJwksCache(stoppingToken);

        // Set up periodic refresh
        using var timer = new PeriodicTimer(_refreshInterval);

        try
        {
            while (await timer.WaitForNextTickAsync(stoppingToken))
            {
                await RefreshJwksCache(stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("JWKS Background Service stopping for Assistant service");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "JWKS Background Service encountered an error in Assistant service");
        }
    }

    private async Task RefreshJwksCache(CancellationToken cancellationToken)
    {
        try
        {
            var supabaseUrl = _configuration["SUPABASE_URL"];
            if (string.IsNullOrEmpty(supabaseUrl))
            {
                _logger.LogWarning("SUPABASE_URL not configured, skipping JWKS refresh");
                return;
            }

            // For JWKS requests, prefer anon key but allow service role key as fallback
            var supabaseApiKey = _configuration["SUPABASE_ANON_KEY"];
            if (string.IsNullOrEmpty(supabaseApiKey))
            {
                supabaseApiKey = _configuration["SUPABASE_SERVICE_ROLE_KEY"];
                if (!string.IsNullOrEmpty(supabaseApiKey))
                {
                    _logger.LogDebug("Using SUPABASE_SERVICE_ROLE_KEY for JWKS requests");
                }
            }

            var keys = await FetchJwksKeys(supabaseUrl, supabaseApiKey, cancellationToken);
            
            // Update cache atomically
            _keyCache.Clear();
            foreach (var (kid, key) in keys)
            {
                _keyCache[kid] = key;
            }

            _logger.LogInformation("JWKS cache refreshed successfully for Assistant service with {Count} keys", keys.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh JWKS cache for Assistant service");
        }
    }

    private async Task<Dictionary<string, SecurityKey>> FetchJwksKeys(string supabaseUrl, string? apiKey, CancellationToken cancellationToken)
    {
        var jwksUri = $"{supabaseUrl}/auth/v1/.well-known/jwks.json";
        
        using var httpClient = _httpClientFactory.CreateClient();
        httpClient.Timeout = TimeSpan.FromSeconds(10);
        
        // Add API key if available
        if (!string.IsNullOrEmpty(apiKey))
        {
            httpClient.DefaultRequestHeaders.Add("apikey", apiKey);
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
        }

        _logger.LogDebug("Fetching JWKS from: {JwksUri}", jwksUri);
        
        var response = await httpClient.GetAsync(jwksUri, cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var jwksJson = await response.Content.ReadAsStringAsync(cancellationToken);

        var options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };

        var jwks = JsonSerializer.Deserialize<JsonWebKeySet>(jwksJson, options);

        var keys = new Dictionary<string, SecurityKey>();

        if (jwks?.Keys != null)
        {
            foreach (var jwk in jwks.Keys)
            {
                if (string.IsNullOrEmpty(jwk.Kid))
                {
                    _logger.LogWarning("Skipping JWK with no Key ID");
                    continue;
                }

                try
                {
                    SecurityKey securityKey;

                    // Handle different key types
                    if (jwk.Kty == "EC" && jwk.Crv == "P-256")
                    {
                        // ES256 keys - use Microsoft.IdentityModel.Tokens.JsonWebKey
                        var jsonWebKey = new Microsoft.IdentityModel.Tokens.JsonWebKey(JsonSerializer.Serialize(jwk, options));
                        securityKey = jsonWebKey;
                    }
                    else if (jwk.Kty == "RSA")
                    {
                        // RSA keys - use Microsoft.IdentityModel.Tokens.JsonWebKey
                        var jsonWebKey = new Microsoft.IdentityModel.Tokens.JsonWebKey(JsonSerializer.Serialize(jwk, options));
                        securityKey = jsonWebKey;
                    }
                    else
                    {
                        _logger.LogWarning("Unsupported key type: {KeyType}, curve: {Curve}", jwk.Kty, jwk.Crv);
                        continue;
                    }

                    keys[jwk.Kid] = securityKey;
                    _logger.LogDebug("Added JWKS key: {KeyId}", jwk.Kid);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process JWKS key: {KeyId}", jwk.Kid);
                }
            }
        }

        return keys;
    }
}

/// <summary>
/// Singleton service to hold the JWKS key cache
/// </summary>
public class JwksKeyCache
{
    public ConcurrentDictionary<string, SecurityKey> Keys { get; } = new();
}

/// <summary>
/// JSON Web Key Set model for deserializing JWKS responses
/// </summary>
public class JsonWebKeySet
{
    public JsonWebKey[]? Keys { get; set; }
}

/// <summary>
/// JSON Web Key model for deserializing individual keys from JWKS
/// </summary>
public class JsonWebKey
{
    public string? Kid { get; set; }
    public string? Kty { get; set; }
    public string? Use { get; set; }
    public string? Alg { get; set; }
    public string? Crv { get; set; }
    public string? X { get; set; }
    public string? Y { get; set; }
    public string? N { get; set; }
    public string? E { get; set; }
}
