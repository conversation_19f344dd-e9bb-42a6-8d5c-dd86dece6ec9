apiVersion: apps/v1
kind: Deployment
metadata:
  name: assistant-service
  namespace: abraapi
  labels:
    app: assistant-service
    service: assistant
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: assistant-service
  template:
    metadata:
      labels:
        app: assistant-service
        service: assistant
        version: v1
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: assistant-service
        image: ghcr.io/arnyfesto1/abraapp-assistant-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: assistant-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: assistant-service-config
              key: ASPNETCORE_URLS
        - name: SUPABASE_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: assistant-service-secret
              key: SUPABASE_CONNECTION_STRING
        - name: <PERSON><PERSON><PERSON><PERSON><PERSON>_URL
          valueFrom:
            secretKeyRef:
              name: assistant-service-secret
              key: SUPABASE_URL
        - name: SUPABASE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: assistant-service-secret
              key: SUPABASE_PROJECT_ID
        - name: SUPABASE_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: assistant-service-secret
              key: SUPABASE_JWT_SECRET
        - name: SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: assistant-service-secret
              key: SUPABASE_ANON_KEY
        - name: SUPABASE_SERVICE_ROLE_KEY
          valueFrom:
            secretKeyRef:
              name: assistant-service-secret
              key: SUPABASE_SERVICE_ROLE_KEY
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 15
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 6
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      imagePullSecrets:
        - name: ghcr-creds
