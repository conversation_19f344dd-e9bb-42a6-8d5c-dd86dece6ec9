using MarketDataService.Modules.Caching.Interfaces;
using MarketDataService.Modules.Caching.Services;
using StackExchange.Redis;

namespace MarketDataService.Modules.Caching;

public static class CachingModule
{
    public static IServiceCollection AddCachingModule(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
    {
        // Configure Redis connection (with fallback for development)
        if (environment.IsDevelopment())
        {
            // Use in-memory cache for development when Redis is not available
            services.AddMemoryCache();
            services.AddSingleton<IRedisCacheService, InMemoryCacheService>();
            // Add structured cache service for application-level caching
            services.AddScoped<ICacheService, MemoryCacheService>();
        }
        else
        {
            services.AddSingleton<IConnectionMultiplexer>(
                _ => ConnectionMultiplexer.Connect(configuration["Redis:ConnectionString"] ?? "localhost:6379"));

            // Register cache services
            services.AddSingleton<RedisCacheService>();
            services.AddSingleton<IRedisCacheService>(provider => provider.GetRequiredService<RedisCacheService>());
            // Add structured cache service for application-level caching (fallback to memory cache in production too)
            services.AddMemoryCache();
            services.AddScoped<ICacheService, MemoryCacheService>();
        }

        // Add memory cache for bulk price operations
        services.AddMemoryCache();

        // Add response caching
        services.AddResponseCaching();

        return services;
    }
}
