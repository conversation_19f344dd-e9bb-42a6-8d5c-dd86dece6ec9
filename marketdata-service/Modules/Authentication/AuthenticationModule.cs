using MarketDataService.Extensions;

namespace MarketDataService.Modules.Authentication;

public static class AuthenticationModule
{
    public static IServiceCollection AddAuthenticationModule(this IServiceCollection services, IConfiguration configuration)
    {
        // Enhanced Supabase JWT Authentication with auto-discovery and improved error handling
        Console.WriteLine("DEBUG: Configuring enhanced Supabase JWT authentication for MarketData service");

        // Log configuration summary for debugging
        var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<Program>();
        logger.LogDebug("Supabase configuration: {@Config}", configuration.GetSupabaseConfigurationSummary());

        // Use the enhanced auto-configuration extension
        services.AddSupabaseJwtAuthentication(configuration);
        services.AddAuthorization();

        return services;
    }
}
