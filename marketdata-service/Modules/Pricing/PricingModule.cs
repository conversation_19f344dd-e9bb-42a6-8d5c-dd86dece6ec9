using MarketDataService.Modules.Pricing.Interfaces;
using MarketDataService.Modules.Pricing.Services;

namespace MarketDataService.Modules.Pricing;

public static class PricingModule
{
    public static IServiceCollection AddPricingModule(this IServiceCollection services, IWebHostEnvironment environment)
    {
        // Register pricing services
        if (environment.IsDevelopment())
        {
            // Mock price service for development (since we don't have real API keys)
            services.AddScoped<IPriceService, MockPriceService>();
            services.AddScoped<IHistoricalPriceRegistry, MockHistoricalPriceRegistry>();
            
            // Real bulk price service
            services.AddScoped<BulkPriceService>();
            services.AddScoped<IBulkPriceService>(provider => provider.GetRequiredService<BulkPriceService>());
        }
        else
        {
            // Production services
            services.AddScoped<PriceService>();
            services.AddScoped<IPriceService>(provider => provider.GetRequiredService<PriceService>());
            services.AddScoped<BulkPriceService>();
            services.AddScoped<IBulkPriceService>(provider => provider.GetRequiredService<BulkPriceService>());
            
            // Historical price registry
            services.AddSingleton<HistoricalPriceRegistry>();
            services.AddSingleton<IHistoricalPriceRegistry>(provider => provider.GetRequiredService<HistoricalPriceRegistry>());
        }

        return services;
    }
}
