using MarketDataService.Configuration;
using MarketDataService.Modules.Core.Services;
using Microsoft.EntityFrameworkCore;

namespace MarketDataService.Modules.Core;

public static class CoreModule
{
    public static IServiceCollection AddCoreModule(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
    {
        // Configure database context using ASP.NET Core configuration
        var connectionString = configuration["SUPABASE_CONNECTION_STRING"]
            ?? configuration.GetConnectionString("DefaultConnection")
            ?? throw new InvalidOperationException("No database connection string found. Set SUPABASE_CONNECTION_STRING environment variable or DefaultConnection in appsettings.json");

        Console.WriteLine($"DEBUG: Using connection string from ASP.NET Core configuration");
        Console.WriteLine($"DEBUG: Connection string: {connectionString}");
        Console.WriteLine($"DEBUG: Connection string length: {connectionString.Length}");
        Console.WriteLine($"DEBUG: Connection string ends with: '{connectionString[Math.Max(0, connectionString.Length - 20)..]}'");

        // Only register PostgreSQL if not in testing environment (tests will register their own InMemory database)
        if (!environment.IsEnvironment("Testing"))
        {
            services.AddDbContext<MarketDataContext>(options =>
            {
                options.UseNpgsql(connectionString, npgsqlOptions =>
                {
                    npgsqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(10),
                        errorCodesToAdd: null);
                    npgsqlOptions.CommandTimeout(30);
                });

                if (environment.IsDevelopment())
                {
                    options.EnableSensitiveDataLogging();
                    options.EnableDetailedErrors();
                }
            });
        }

        // Configure settings
        services.Configure<BrokerSettings>(configuration.GetSection("BrokerSettings"));
        services.Configure<BrokerApiKeys>(configuration.GetSection("BrokerApiKeys"));

        return services;
    }
}
