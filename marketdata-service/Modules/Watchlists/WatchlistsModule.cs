using MarketDataService.Modules.Watchlists.Interfaces;
using MarketDataService.Modules.Watchlists.Services;

namespace MarketDataService.Modules.Watchlists;

public static class WatchlistsModule
{
    public static IServiceCollection AddWatchlistsModule(this IServiceCollection services, IWebHostEnvironment environment)
    {
        // Register watchlist services
        if (environment.IsDevelopment())
        {
            // Use real services with Supabase database
            services.AddScoped<WatchlistService>();
            services.AddScoped<IWatchlistService>(provider => provider.GetRequiredService<WatchlistService>());
        }
        else
        {
            // Production services
            services.AddScoped<WatchlistService>();
            services.AddScoped<IWatchlistService>(provider => provider.GetRequiredService<WatchlistService>());
        }

        return services;
    }
}
