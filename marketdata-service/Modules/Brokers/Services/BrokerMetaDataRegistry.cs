using MarketDataService.Interfaces;
using MarketDataService.Models;

namespace MarketDataService.Services;

public class BrokerMetadataRegistry(IEnumerable<IBrokerMetadataProvider> providers) : IBrokerMetadataRegistry
{
    private readonly List<IBrokerMetadataProvider> _providers = providers.ToList();

    public IEnumerable<BrokerMetadata> ListAll() =>
        _providers.Select(p => p.GetMetadata());
}