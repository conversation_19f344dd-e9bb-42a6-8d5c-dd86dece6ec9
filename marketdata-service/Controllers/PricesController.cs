using MarketDataService.Interfaces;
using MarketDataService.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace MarketDataService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PricesController(
    IPriceService priceService, 
    IBulkPriceService bulkPriceService,
    ILogger<PricesController> logger) : ControllerBase
{
    private readonly IPriceService _priceService = priceService;
    private readonly IBulkPriceService _bulkPriceService = bulkPriceService;
    private readonly ILogger<PricesController> _logger = logger;

    /// <summary>
    /// Get current price for a single symbol
    /// </summary>
    [HttpGet("{symbol}")]
    [Authorize]
    [ResponseCache(Duration = 60, VaryByQueryKeys = new[] { "symbol", "nocache" })]
    public async Task<IActionResult> GetPrice(
        [FromRoute] string symbol,
        [FromQuery] bool nocache = false)
    {
        try
        {
            var brokerId = User.FindFirst("broker_id")?.Value;
            if (string.IsNullOrWhiteSpace(brokerId))
            {
                // Use default broker if none is linked to user
                brokerId = "finnhub"; // Default to Finnhub provider
                _logger.LogDebug("No broker_id found in user claims, using default broker: {BrokerId}", brokerId);
            }
            else
            {
                _logger.LogDebug("Using broker from user claims: {BrokerId}", brokerId);
            }

            var result = await _priceService.GetLatestPriceAsync(symbol, brokerId, nocache);
            return result == null ? Problem("Failed to fetch price.") : Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting price for symbol {Symbol}", symbol);
            return StatusCode(500, "Failed to get price");
        }
    }

    /// <summary>
    /// Get bulk prices for multiple symbols
    /// </summary>
    [HttpGet("bulk")]
    [Authorize]
    [ResponseCache(Duration = 60, VaryByQueryKeys = new[] { "symbols", "nocache" })]
    public async Task<IActionResult> GetBulkPrices(
        [FromQuery] string symbols,
        [FromQuery] bool nocache = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(symbols))
                return BadRequest("Symbols parameter is required");

            var symbolList = symbols.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                   .Select(s => s.Trim().ToUpper())
                                   .ToList();

            if (!symbolList.Any())
                return BadRequest("At least one symbol is required");

            if (symbolList.Count > 100)
                return BadRequest("Maximum 100 symbols allowed per request");

            var brokerId = User.FindFirst("broker_id")?.Value;
            var response = await _bulkPriceService.GetBulkPricesAsync(symbolList, brokerId, nocache);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting bulk prices for symbols {Symbols}", symbols);
            return StatusCode(500, "Failed to get bulk prices");
        }
    }

    /// <summary>
    /// Get bulk prices for multiple symbols (POST version for large requests)
    /// </summary>
    [HttpPost("bulk")]
    [Authorize]
    public async Task<IActionResult> PostBulkPrices([FromBody] BulkPriceRequest request)
    {
        try
        {
            if (request.Symbols == null || !request.Symbols.Any())
                return BadRequest("At least one symbol is required");

            if (request.Symbols.Count > 100)
                return BadRequest("Maximum 100 symbols allowed per request");

            var brokerId = User.FindFirst("broker_id")?.Value ?? request.BrokerId;
            var response = await _bulkPriceService.GetBulkPricesAsync(request.Symbols, brokerId, request.BypassCache);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting bulk prices");
            return StatusCode(500, "Failed to get bulk prices");
        }
    }
}
