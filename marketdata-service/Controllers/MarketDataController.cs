using MarketDataService.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace MarketDataService.Controllers;

[ApiController]
[Route("api/marketdata")]
public class MarketDataController(IPriceService priceService, IHistoricalPriceRegistry historyRegistry, ILogger<MarketDataController> logger) : ControllerBase
{
    private readonly IPriceService _priceService = priceService;
    private readonly IHistoricalPriceRegistry _historyRegistry = historyRegistry;
    private readonly ILogger<MarketDataController> _logger = logger;

    [HttpGet("{symbol}/fetch-live")]
    [Authorize]
    [ResponseCache(Duration = 60, VaryByQueryKeys = new[] { "symbol", "nocache" })]
    public async Task<IActionResult> FetchLive(
        [FromRoute] string symbol,
        [FromQuery] bool nocache = false)
    {
        var brokerId = User.FindFirst("broker_id")?.Value;
        if (string.IsNullOrWhiteSpace(brokerId))
        {
            // Use default broker if none is linked to user
            brokerId = "finnhub"; // Default to Finnhub provider
            _logger.LogDebug("No broker_id found in user claims, using default broker: {BrokerId}", brokerId);
        }
        else
        {
            _logger.LogDebug("Using broker from user claims: {BrokerId}", brokerId);
        }

        var result = await _priceService.GetLatestPriceAsync(symbol, brokerId, nocache);
        return result == null ? Problem("Fetch failed.") : Ok(result);
    }

    [HttpGet("{symbol}/history")]
    [Authorize]
    [ResponseCache(Duration = 300, VaryByQueryKeys = new[] { "symbol", "from", "to", "interval" })]
    public async Task<IActionResult> History(
        [FromRoute] string symbol,
        [FromQuery] DateTime from,
        [FromQuery] DateTime to,
        [FromQuery] string interval = "1d")
    {
        var brokerId = User.FindFirst("broker_id")?.Value;
        if (string.IsNullOrWhiteSpace(brokerId))
        {
            // Use default broker if none is linked to user
            brokerId = "finnhub"; // Default to Finnhub provider
            _logger.LogDebug("No broker_id found in user claims for history request, using default broker: {BrokerId}", brokerId);
        }
        else
        {
            _logger.LogDebug("Using broker from user claims for history request: {BrokerId}", brokerId);
        }

        var provider = _historyRegistry.GetProvider(brokerId);
        if (provider == null) return BadRequest($"No provider for broker '{brokerId}'");

        var data = await provider.GetHistoricalPricesAsync(symbol, from, to, interval);
        return data == null ? Problem("Failed to fetch history.") : Ok(data);
    }
}