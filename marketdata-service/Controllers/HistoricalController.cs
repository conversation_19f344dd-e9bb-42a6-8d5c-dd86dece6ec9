using MarketDataService.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace MarketDataService.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class HistoricalController(
    IHistoricalPriceRegistry historyRegistry,
    ILogger<HistoricalController> logger) : ControllerBase
{
    private readonly IHistoricalPriceRegistry _historyRegistry = historyRegistry;
    private readonly ILogger<HistoricalController> _logger = logger;

    /// <summary>
    /// Get historical data for a symbol
    /// </summary>
    [HttpGet("{symbol}")]
    [ResponseCache(Duration = 300, VaryByQueryKeys = new[] { "symbol", "from", "to", "interval" })]
    public async Task<IActionResult> GetHistoricalData(
        [FromRoute] string symbol,
        [FromQuery] DateTime from,
        [FromQuery] DateTime to,
        [FromQuery] string interval = "1d")
    {
        try
        {
            var brokerId = User.FindFirst("broker_id")?.Value;
            if (string.IsNullOrWhiteSpace(brokerId))
            {
                // Use default broker if none is linked to user
                brokerId = "finnhub"; // Default to Finnhub provider
                _logger.LogDebug("No broker_id found in user claims for history request, using default broker: {BrokerId}", brokerId);
            }
            else
            {
                _logger.LogDebug("Using broker from user claims for history request: {BrokerId}", brokerId);
            }

            var provider = _historyRegistry.GetProvider(brokerId);
            if (provider == null) 
                return BadRequest($"No provider for broker '{brokerId}'");

            var data = await provider.GetHistoricalPricesAsync(symbol, from, to, interval);
            return data == null ? Problem("Failed to fetch historical data.") : Ok(data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting historical data for symbol {Symbol}", symbol);
            return StatusCode(500, "Failed to get historical data");
        }
    }
}
