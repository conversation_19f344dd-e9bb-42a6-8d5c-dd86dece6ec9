using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MarketDataService.Data;
using MarketDataService.Interfaces;
using MarketDataService.Models;

namespace MarketDataService.Services;

public class WatchlistService(MarketDataContext context, ILogger<WatchlistService> logger, IRedisCacheService cache) : IWatchlistService
{
    private readonly MarketDataContext _context = context;
    private readonly ILogger<WatchlistService> _logger = logger;
    private readonly IRedisCacheService _cache = cache;

    public async Task<bool> TestDatabaseConnectionAsync()
    {
        try
        {
            // Simple test to check if we can connect to the database
            await _context.Database.CanConnectAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database connection test failed");
            return false;
        }
    }

    public async Task<List<WatchlistDto>> GetUserWatchlistsAsync(Guid userId)
    {
        try
        {
            var cacheKey = $"watchlists:user_and_global:{userId}";

            // Try to get from cache first
            var cachedWatchlists = await _cache.GetAsync<List<WatchlistDto>>(cacheKey);
            if (cachedWatchlists != null)
            {
                _logger.LogDebug("Cache hit for user and global watchlists: {UserId}", userId);
                return cachedWatchlists;
            }

            // Fetch both user-specific and global watchlists
            var watchlists = await _context.Watchlists
                .Where(w => w.UserId == userId || (w.IsGlobal.HasValue && w.IsGlobal.Value))
                .Include(w => w.Items)
                .OrderBy(w => w.CreatedAt)
                .ToListAsync();

            var result = watchlists.Select(w => new WatchlistDto
            {
                Id = w.Id,
                Name = w.Name,
                CreatedAt = w.CreatedAt,
                UpdatedAt = w.UpdatedAt,
                IsGlobal = w.IsGlobal,
                ItemCount = w.Items.Count,
                Items = [.. w.Items.OrderBy(item => item.AddedAt).Select((item, index) => new WatchlistItemWithPriceDto
                {
                    Id = item.Id,
                    Symbol = item.Symbol,
                    Broker = item.Broker,
                    AddedAt = item.AddedAt,
                    SortOrder = index + 1 // Generate sort order based on position
                })]
            }).ToList();

            // Cache for 10 minutes
            await _cache.SetAsync(cacheKey, result, TimeSpan.FromMinutes(10));
            _logger.LogDebug("Cached user and global watchlists: {UserId}", userId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user and global watchlists for user {UserId}", userId);
            throw;
        }
    }

    public async Task<WatchlistDto?> GetWatchlistAsync(Guid userId, long watchlistId, bool includePrices = false)
    {
        try
        {
            var watchlist = await _context.Watchlists
                .Include(w => w.Items)
                .FirstOrDefaultAsync(w => w.Id == watchlistId && w.UserId == userId);

            if (watchlist == null) return null;

            return new WatchlistDto
            {
                Id = watchlist.Id,
                Name = watchlist.Name,
                CreatedAt = watchlist.CreatedAt,
                UpdatedAt = watchlist.UpdatedAt,
                IsGlobal = watchlist.IsGlobal,
                ItemCount = watchlist.Items.Count,
                Items = [.. watchlist.Items
                    .OrderBy(item => item.AddedAt)
                    .Select((item, index) => new WatchlistItemWithPriceDto
                    {
                        Id = item.Id,
                        Symbol = item.Symbol,
                        Broker = item.Broker,
                        AddedAt = item.AddedAt,
                        SortOrder = index + 1 // Generate sort order based on position
                    })]
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting watchlist {WatchlistId} for user {UserId}", watchlistId, userId);
            throw;
        }
    }

    public async Task<WatchlistDto> CreateWatchlistAsync(Guid userId, CreateWatchlistRequest request)
    {
        try
        {
            // Check if user already has a watchlist with this name
            var existingWatchlist = await _context.Watchlists
                .FirstOrDefaultAsync(w => w.UserId == userId && w.Name == request.Name);

            if (existingWatchlist != null)
            {
                throw new InvalidOperationException($"Watchlist '{request.Name}' already exists");
            }

            // If this is set as global, unset other global watchlists
            if (request.IsGlobal == true)
            {
                await UnsetGlobalWatchlistsAsync(userId);
            }

            var watchlist = new Watchlist
            {
                UserId = userId,
                Name = request.Name,
                IsGlobal = request.IsGlobal,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Watchlists.Add(watchlist);
            await _context.SaveChangesAsync();

            // Add symbols if provided
            if (request.Symbols.Any())
            {
                await AddSymbolsToWatchlistAsync(watchlist.Id, userId, request.Symbols);
            }

            // Invalidate cache since we created a new watchlist
            await InvalidateUserWatchlistCache(userId);

            return await GetWatchlistAsync(userId, watchlist.Id) ?? throw new InvalidOperationException("Failed to retrieve created watchlist");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating watchlist for user {UserId}", userId);
            throw;
        }
    }

    public async Task<WatchlistDto?> UpdateWatchlistAsync(Guid userId, long watchlistId, UpdateWatchlistRequest request)
    {
        try
        {
            var watchlist = await _context.Watchlists
                .FirstOrDefaultAsync(w => w.Id == watchlistId && w.UserId == userId);

            if (watchlist == null)
            {
                return null;
            }

            // Update name if provided
            if (!string.IsNullOrEmpty(request.Name) && request.Name != watchlist.Name)
            {
                // Check if another watchlist with this name exists
                var existingWithName = await _context.Watchlists
                    .FirstOrDefaultAsync(w => w.UserId == userId && w.Name == request.Name && w.Id != watchlistId);

                if (existingWithName != null)
                {
                    throw new InvalidOperationException($"Watchlist '{request.Name}' already exists");
                }

                watchlist.Name = request.Name;
            }

            // Update global status if provided
            if (request.IsGlobal.HasValue)
            {
                if (request.IsGlobal.Value)
                {
                    await UnsetGlobalWatchlistsAsync(userId);
                }
                watchlist.IsGlobal = request.IsGlobal.Value;
            }

            watchlist.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            // Invalidate cache since we updated the watchlist
            await InvalidateUserWatchlistCache(userId);

            return await GetWatchlistAsync(userId, watchlistId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating watchlist {WatchlistId} for user {UserId}", watchlistId, userId);
            throw;
        }
    }

    public async Task<bool> DeleteWatchlistAsync(Guid userId, long watchlistId)
    {
        try
        {
            var watchlist = await _context.Watchlists
                .Include(w => w.Items)
                .FirstOrDefaultAsync(w => w.Id == watchlistId && w.UserId == userId);

            if (watchlist == null) return false;

            _context.Watchlists.Remove(watchlist);
            await _context.SaveChangesAsync();

            // Invalidate cache
            await InvalidateUserWatchlistCache(userId);

            _logger.LogInformation("Deleted watchlist {WatchlistId} for user {UserId}", watchlistId, userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting watchlist {WatchlistId} for user {UserId}", watchlistId, userId);
            throw;
        }
    }

    public async Task<WatchlistDto?> AddSymbolsAsync(Guid userId, long watchlistId, AddSymbolsRequest request)
    {
        try
        {
            await AddSymbolsToWatchlistAsync(watchlistId, userId, request.Symbols);
            return await GetWatchlistAsync(userId, watchlistId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding symbols to watchlist {WatchlistId} for user {UserId}", watchlistId, userId);
            throw;
        }
    }

    public async Task<bool> RemoveSymbolAsync(Guid userId, long watchlistId, string symbol)
    {
        try
        {
            var watchlist = await _context.Watchlists
                .Include(w => w.Items)
                .FirstOrDefaultAsync(w => w.Id == watchlistId && w.UserId == userId);

            if (watchlist == null)
            {
                return false;
            }

            var item = watchlist.Items.FirstOrDefault(i => i.Symbol == symbol);
            if (item != null)
            {
                _context.WatchlistItems.Remove(item);
                watchlist.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
                
                // Invalidate cache since we removed a symbol
                await InvalidateUserWatchlistCache(userId);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing symbol {Symbol} from watchlist {WatchlistId} for user {UserId}", symbol, watchlistId, userId);
            throw;
        }
    }

    public async Task<WatchlistDto?> ReorderItemsAsync(Guid userId, long watchlistId, ReorderItemsRequest request)
    {
        try
        {
            var watchlist = await _context.Watchlists
                .Include(w => w.Items)
                .FirstOrDefaultAsync(w => w.Id == watchlistId && w.UserId == userId);

            if (watchlist == null)
            {
                return null;
            }

            // Note: Database doesn't have sort_order column, so reordering is not supported
            // Items are ordered by added_at timestamp instead
            _logger.LogWarning("Reordering is not supported - items are ordered by added_at timestamp");

            // Just return the current watchlist without changes

            return await GetWatchlistAsync(userId, watchlistId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering items in watchlist {WatchlistId} for user {UserId}", watchlistId, userId);
            throw;
        }
    }

    public async Task<WatchlistDto?> GetGlobalWatchlistAsync(Guid userId, bool includePrices = false)
    {
        try
        {
            var globalWatchlist = await _context.Watchlists
                .Include(w => w.Items)
                .FirstOrDefaultAsync(w => w.UserId == userId && w.IsGlobal == true);

            if (globalWatchlist == null) return null;

            return new WatchlistDto
            {
                Id = globalWatchlist.Id,
                Name = globalWatchlist.Name,
                CreatedAt = globalWatchlist.CreatedAt,
                UpdatedAt = globalWatchlist.UpdatedAt,
                IsGlobal = globalWatchlist.IsGlobal,
                ItemCount = globalWatchlist.Items.Count,
                Items = globalWatchlist.Items
                    .OrderBy(item => item.AddedAt)
                    .Select((item, index) => new WatchlistItemWithPriceDto
                    {
                        Id = item.Id,
                        Symbol = item.Symbol,
                        Broker = item.Broker,
                        AddedAt = item.AddedAt,
                        SortOrder = index + 1 // Generate sort order based on position
                    }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting global watchlist for user {UserId}", userId);
            throw;
        }
    }

    private async Task AddSymbolsToWatchlistAsync(long watchlistId, Guid userId, List<string> symbols)
    {
        var watchlist = await _context.Watchlists
            .Include(w => w.Items)
            .FirstOrDefaultAsync(w => w.Id == watchlistId && w.UserId == userId);

        if (watchlist == null)
        {
            throw new ArgumentException($"Watchlist {watchlistId} not found");
        }

        var existingSymbols = watchlist.Items.Select(i => i.Symbol).ToHashSet();
        var newSymbols = symbols.Where(s => !existingSymbols.Contains(s)).ToList();

        if (newSymbols.Any())
        {
            foreach (var symbol in newSymbols)
            {
                var item = new WatchlistItem
                {
                    WatchlistId = watchlistId,
                    Symbol = symbol,
                    AddedAt = DateTime.UtcNow
                };

                _context.WatchlistItems.Add(item);
            }

            watchlist.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            
            // Invalidate cache since we added symbols
            await InvalidateUserWatchlistCache(userId);
        }
    }

    private async Task UnsetGlobalWatchlistsAsync(Guid userId)
    {
        var globalWatchlists = await _context.Watchlists
            .Where(w => w.UserId == userId && w.IsGlobal == true)
            .ToListAsync();

        foreach (var watchlist in globalWatchlists)
        {
            watchlist.IsGlobal = false;
            watchlist.UpdatedAt = DateTime.UtcNow;
        }

        if (globalWatchlists.Any())
        {
            await _context.SaveChangesAsync();
        }
    }

    private async Task InvalidateUserWatchlistCache(Guid userId)
    {
        var cacheKey = $"watchlists:user:{userId}";
        await _cache.RemoveAsync(cacheKey);
        _logger.LogDebug("Invalidated watchlist cache for user: {UserId}", userId);
    }
}
