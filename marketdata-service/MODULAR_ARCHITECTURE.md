# Market Data Service - Modular Architecture

## Overview

The Market Data Service has been refactored from a monolithic structure to a modular architecture. This improves maintainability, testability, and separation of concerns.

## Module Structure

### 1. Core Module (`Modules/Core/`)
**Purpose**: Shared infrastructure, database context, and common models

**Components**:
- `Models/MarketData.cs` - Core market data model
- `Services/MarketDataContext.cs` - Entity Framework database context
- `CoreModule.cs` - Module registration and configuration

**Responsibilities**:
- Database configuration and connection
- Shared models used across modules
- Configuration settings (BrokerSettings, BrokerApiKeys)

### 2. Symbols Module (`Modules/Symbols/`)
**Purpose**: Symbol management, search, and registry functionality

**Components**:
- `Controllers/SymbolController.cs` - Symbol API endpoints
- `Services/SymbolRegistry.cs` - Symbol provider registry
- `Interfaces/ISymbolRegistry.cs`, `ISymbolProvider.cs` - Symbol interfaces
- `Models/Symbol.cs` - Symbol-related models and DTOs
- `SymbolsModule.cs` - Module registration

**Responsibilities**:
- Symbol search and discovery
- Market type filtering (stocks, forex, crypto)
- Active symbol tracking
- Multi-broker symbol aggregation

### 3. Watchlists Module (`Modules/Watchlists/`)
**Purpose**: User watchlist management and operations

**Components**:
- `Controllers/WatchlistController.cs` - Watchlist API endpoints
- `Services/WatchlistService.cs` - Watchlist business logic
- `Interfaces/IWatchlistService.cs` - Watchlist interface
- `Models/Watchlist.cs`, `WatchlistDtos.cs` - Watchlist models and DTOs
- `WatchlistsModule.cs` - Module registration

**Responsibilities**:
- CRUD operations for watchlists
- User-specific watchlist management
- Watchlist item management
- Integration with pricing for watchlist prices

### 4. Pricing Module (`Modules/Pricing/`)
**Purpose**: Price services, bulk pricing, and historical data

**Components**:
- `Controllers/PricesController.cs`, `HistoricalController.cs` - Pricing API endpoints
- `Services/PriceService.cs`, `BulkPriceService.cs` - Pricing business logic
- `Interfaces/IPriceService.cs`, `IBulkPriceService.cs`, etc. - Pricing interfaces
- `Models/HistoricalPrice.cs` - Pricing models
- `PricingModule.cs` - Module registration

**Responsibilities**:
- Real-time price fetching
- Bulk price operations
- Historical price data
- Price caching and optimization
- Mock services for development

### 5. Brokers Module (`Modules/Brokers/`)
**Purpose**: External broker integrations and metadata

**Components**:
- `Controllers/BrokersController.cs` - Broker API endpoints
- `Services/BrokerRegistry.cs`, `BrokerMetadataRegistry.cs` - Broker registries
- `Interfaces/IBrokerRegistry.cs`, `IBrokerMetadataProvider.cs` - Broker interfaces
- `Models/BrokerMetaData.cs` - Broker models
- `BrokersModule.cs` - Module registration

**Responsibilities**:
- Broker provider management (Finnhub, Polygon)
- Broker metadata and capabilities
- Multi-broker data aggregation
- External API integration

### 6. Caching Module (`Modules/Caching/`)
**Purpose**: Caching services and management

**Components**:
- `Services/RedisCacheService.cs`, `InMemoryCacheService.cs` - Cache implementations
- `Interfaces/IRedisCacheService.cs`, `ICacheService.cs` - Cache interfaces
- `CachingModule.cs` - Module registration

**Responsibilities**:
- Redis caching for production
- In-memory caching for development
- Response caching
- Cache invalidation strategies

### 7. Authentication Module (`Modules/Authentication/`)
**Purpose**: JWT authentication and authorization

**Components**:
- `Services/SupabaseJwtConfiguration.cs`, `JwksBackgroundService.cs` - Auth services
- `AuthenticationModule.cs` - Module registration

**Responsibilities**:
- Supabase JWT authentication
- Token validation and parsing
- User authorization
- JWKS background refresh

## Benefits of Modular Architecture

### 1. **Separation of Concerns**
- Each module has a single, well-defined responsibility
- Clear boundaries between different functional areas
- Easier to understand and maintain

### 2. **Improved Testability**
- Modules can be tested in isolation
- Mock dependencies are easier to inject
- Unit tests are more focused and reliable

### 3. **Better Maintainability**
- Changes in one module don't affect others
- Easier to locate and fix bugs
- Cleaner code organization

### 4. **Scalability**
- Modules can be developed by different teams
- Easier to add new features without affecting existing ones
- Potential for future microservice extraction

### 5. **Dependency Management**
- Clear dependency injection per module
- Easier to manage service lifetimes
- Reduced coupling between components

## Usage

### Development
The modular structure is fully backward compatible. All existing API endpoints work exactly the same.

### Module Registration
Each module registers its services through extension methods:

```csharp
// In Program.cs
builder.Services.AddCoreModule(builder.Configuration, builder.Environment);
builder.Services.AddCachingModule(builder.Configuration, builder.Environment);
builder.Services.AddAuthenticationModule(builder.Configuration);
builder.Services.AddBrokersModule();
builder.Services.AddSymbolsModule();
builder.Services.AddPricingModule(builder.Environment);
builder.Services.AddWatchlistsModule(builder.Environment);
```

### Adding New Features
1. Identify the appropriate module for your feature
2. Add your components to the module's directory structure
3. Update the module's registration method if needed
4. Follow the existing namespace conventions

## Migration Notes

- All existing functionality remains unchanged
- API endpoints are the same
- Database schema is unchanged
- Configuration remains the same
- Docker deployment is unchanged

## Future Enhancements

1. **Module-specific Configuration**: Each module could have its own configuration section
2. **Plugin Architecture**: Modules could be loaded dynamically
3. **Microservice Extraction**: Individual modules could be extracted as separate services
4. **Module-specific Middleware**: Each module could register its own middleware
5. **Module Health Checks**: Individual module health monitoring
