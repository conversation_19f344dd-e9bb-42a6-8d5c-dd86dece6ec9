using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using AuthService.Models;
using AuthService.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Moq;
using Xunit;

namespace AuthService.Tests.Integration;

public class TokenRefreshIntegrationTests : IDisposable
{
    private const string TestJwtSecret = "test-jwt-secret-key-for-testing-purposes-only-not-for-production-use";
    private const string TestSupabaseUrl = "https://test.supabase.co";
    
    private readonly Mock<ISupabaseClient> _supabaseClientMock;
    private readonly WebApplicationFactory<Program> _factory;

    public TokenRefreshIntegrationTests()
    {
        _supabaseClientMock = new Mock<ISupabaseClient>();

        _factory = new WebApplicationFactory<Program>().WithWebHostBuilder(builder =>
        {
            builder.UseEnvironment("Testing");
            
            // Set test configuration
            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["SUPABASE_URL"] = TestSupabaseUrl,
                    ["SUPABASE_PROJECT_ID"] = "authenticated",
                    ["Supabase:Url"] = TestSupabaseUrl,
                    ["Supabase:JwtSecret"] = TestJwtSecret
                });
            });

            builder.ConfigureServices(services =>
            {
                // Remove existing ISupabaseClient registrations
                var descriptorsToRemove = services.Where(d =>
                    d.ServiceType == typeof(ISupabaseClient) ||
                    d.ImplementationType == typeof(SupabaseClient)).ToList();

                foreach (var descriptor in descriptorsToRemove)
                {
                    services.Remove(descriptor);
                }

                // Add our mock
                services.AddSingleton<ISupabaseClient>(_supabaseClientMock.Object);
                
                // Add HttpClient services if they were removed
                services.AddHttpClient();
                
                // Add JwksCache and JwksService with valid JWKS URI
                services.AddSingleton<JwksCache>(provider => {
                    var logger = provider.GetRequiredService<ILogger<JwksCache>>();
                    return new JwksCache(logger);
                });
                
                services.AddSingleton<JwksService>(provider => {
                    var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
                    var httpClient = httpClientFactory.CreateClient();
                    var memoryCache = provider.GetRequiredService<IMemoryCache>();
                    var logger = provider.GetRequiredService<ILogger<JwksService>>();
                    var jwksCache = provider.GetRequiredService<JwksCache>();
                    var jwksUri = $"{TestSupabaseUrl}/auth/v1/.well-known/jwks.json";
                    
                    return new JwksService(
                        httpClient,
                        memoryCache,
                        logger,
                        jwksUri,
                        TimeSpan.FromHours(24),
                        jwksCache
                    );
                });
                
                // Configure JWT Bearer options for testing
                services.Configure<Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerOptions>(
                    Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerDefaults.AuthenticationScheme,
                    options =>
                    {
                        options.RequireHttpsMetadata = false; // Disable HTTPS requirement for testing
                        options.Authority = $"{TestSupabaseUrl}/auth/v1";
                        options.Audience = "authenticated";
                    });
            });
        });
    }

    public void Dispose()
    {
        _factory?.Dispose();
    }

    [Fact]
    public async Task RefreshToken_WithValidRefreshToken_ReturnsNewTokens()
    {
        // Arrange
        var refreshToken = "valid-refresh-token";
        var newAccessToken = GenerateValidJwtToken("refreshed-user-id", "<EMAIL>");
        var newRefreshToken = "new-refresh-token";

        var expectedResponse = new AuthResponse
        {
            AccessToken = newAccessToken,
            RefreshToken = newRefreshToken,
            TokenType = "bearer",
            ExpiresIn = 3600
        };

        _supabaseClientMock
            .Setup(x => x.RefreshTokenAsync(It.Is<TokenRefreshRequest>(r => r.RefreshToken == refreshToken)))
            .ReturnsAsync(expectedResponse);

        var client = _factory.CreateClient();
        var request = new TokenRefreshRequest { RefreshToken = refreshToken };
        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await client.PostAsync("/api/auth/token/refresh", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        var authResponse = JsonSerializer.Deserialize<AuthResponse>(responseContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        authResponse.Should().NotBeNull();
        authResponse!.AccessToken.Should().Be(newAccessToken);
        authResponse.RefreshToken.Should().Be(newRefreshToken);
        authResponse.TokenType.Should().Be("bearer");
        authResponse.ExpiresIn.Should().Be(3600);

        // Verify the mock was called
        _supabaseClientMock.Verify(x => x.RefreshTokenAsync(It.IsAny<TokenRefreshRequest>()), Times.Once);
    }

    [Fact]
    public async Task RefreshToken_WithInvalidRefreshToken_ReturnsBadRequest()
    {
        // Arrange
        var invalidRefreshToken = "invalid-refresh-token";

        _supabaseClientMock
            .Setup(x => x.RefreshTokenAsync(It.IsAny<TokenRefreshRequest>()))
            .ThrowsAsync(new HttpRequestException("Token refresh failed: Invalid refresh token"));

        var client = _factory.CreateClient();
        var request = new TokenRefreshRequest { RefreshToken = invalidRefreshToken };
        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await client.PostAsync("/api/auth/token/refresh", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task RefreshToken_WithEmptyRefreshToken_ReturnsBadRequest()
    {
        // Arrange
        var client = _factory.CreateClient();
        var request = new TokenRefreshRequest { RefreshToken = "" };
        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await client.PostAsync("/api/auth/token/refresh", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.Should().Contain("Refresh token is required");
    }

    [Fact]
    public void GeneratedToken_ShouldBeValidForAuthentication()
    {
        // This test verifies that tokens generated with our test parameters
        // can be validated by our JWT configuration
        
        // Arrange
        var userId = "test-user-id";
        var email = "<EMAIL>";
        var token = GenerateValidJwtToken(userId, email);

        // Act & Assert - Validate the token using the same parameters as the server
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(TestJwtSecret);
        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = $"{TestSupabaseUrl}/auth/v1",
            ValidateAudience = true,
            ValidAudience = "authenticated",
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(key),
            ClockSkew = TimeSpan.FromMinutes(5),
            RequireExpirationTime = true,
            RequireSignedTokens = true,
            ValidAlgorithms = new[] { SecurityAlgorithms.HmacSha256 }
        };

        // This should not throw
        var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
        
        principal.Should().NotBeNull();
        principal.FindFirst("sub")?.Value.Should().Be(userId);
        principal.FindFirst("email")?.Value.Should().Be(email);
    }

    private string GenerateValidJwtToken(string userId, string email)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(TestJwtSecret);
        var signingKey = new SymmetricSecurityKey(key);
        var credentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim("aud", "authenticated"),
            new Claim("exp", DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new Claim("sub", userId),
            new Claim("email", email),
            new Claim("role", "authenticated"),
            new Claim("aal", "aal1")
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddHours(1),
            Issuer = $"{TestSupabaseUrl}/auth/v1",
            Audience = "authenticated",
            SigningCredentials = credentials
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }
}
