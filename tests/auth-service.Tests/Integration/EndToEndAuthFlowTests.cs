using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using AuthService.Models;
using AuthService.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Moq;
using Xunit;

namespace AuthService.Tests.Integration;

public class EndToEndAuthFlowTests : IDisposable
{
    private const string TestJwtSecret = "test-jwt-secret-key-for-testing-purposes-only-not-for-production-use";
    private const string TestSupabaseUrl = "https://test.supabase.co";
    
    private readonly Mock<ISupabaseClient> _supabaseClientMock;
    private readonly WebApplicationFactory<Program> _factory;

    public EndToEndAuthFlowTests()
    {
        _supabaseClientMock = new Mock<ISupabaseClient>();

        _factory = new WebApplicationFactory<Program>().WithWebHostBuilder(builder =>
        {
            builder.UseEnvironment("Testing");

            builder.ConfigureAppConfiguration((context, config) =>
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["SUPABASE_URL"] = TestSupabaseUrl,
                    ["SUPABASE_PROJECT_ID"] = "authenticated",
                    ["Supabase:Url"] = TestSupabaseUrl,
                    ["Supabase:JwtSecret"] = TestJwtSecret
                });
            });

            builder.ConfigureServices(services =>
            {
                // Remove existing ISupabaseClient registrations
                var descriptorsToRemove = services.Where(d =>
                    d.ServiceType == typeof(ISupabaseClient) ||
                    d.ImplementationType == typeof(SupabaseClient)).ToList();

                foreach (var descriptor in descriptorsToRemove)
                {
                    services.Remove(descriptor);
                }

                services.AddSingleton<ISupabaseClient>(_supabaseClientMock.Object);
                
                // Add HttpClient services if they were removed
                services.AddHttpClient();
                
                // Add JwksCache and JwksService with valid JWKS URI
                services.AddSingleton<JwksCache>(provider => {
                    var logger = provider.GetRequiredService<ILogger<JwksCache>>();
                    return new JwksCache(logger);
                });
                
                services.AddSingleton<JwksService>(provider => {
                    var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
                    var httpClient = httpClientFactory.CreateClient();
                    var memoryCache = provider.GetRequiredService<IMemoryCache>();
                    var logger = provider.GetRequiredService<ILogger<JwksService>>();
                    var jwksCache = provider.GetRequiredService<JwksCache>();
                    var jwksUri = $"{TestSupabaseUrl}/auth/v1/.well-known/jwks.json";
                    
                    return new JwksService(
                        httpClient,
                        memoryCache,
                        logger,
                        jwksUri,
                        TimeSpan.FromHours(24),
                        jwksCache
                    );
                });

                // Configure JWT Bearer options for testing
                services.Configure<Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerOptions>(
                    Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerDefaults.AuthenticationScheme,
                    options =>
                    {
                        options.RequireHttpsMetadata = false; // Disable HTTPS requirement for testing
                        options.Authority = $"{TestSupabaseUrl}/auth/v1";
                        options.Audience = "authenticated";
                    });

                // For JWT validation tests, we'll use the real JWT middleware
                // but for other tests we can use the test authentication
            });
        });
    }

    public void Dispose()
    {
        _factory?.Dispose();
    }

    [Fact]
    public async Task CompleteAuthFlow_LoginRefreshProfile_ShouldWorkEndToEnd()
    {
        // This test simulates the complete authentication flow:
        // 1. Login -> get tokens
        // 2. Use access token to get profile
        // 3. Refresh tokens
        // 4. Use new access token to get profile again

        var client = _factory.CreateClient();
        var userId = "test-user-123";
        var email = "<EMAIL>";

        // Step 1: Login
        var loginRequest = new LoginRequest { Email = email, Password = "password123" };
        var initialAccessToken = GenerateValidJwtToken(userId, email);
        var initialRefreshToken = "initial-refresh-token";

        var loginResponse = new AuthResponse
        {
            AccessToken = initialAccessToken,
            RefreshToken = initialRefreshToken,
            TokenType = "bearer",
            ExpiresIn = 3600
        };

        _supabaseClientMock
            .Setup(x => x.LoginAsync(It.IsAny<LoginRequest>()))
            .ReturnsAsync(loginResponse);

        var loginJson = JsonSerializer.Serialize(loginRequest);
        var loginContent = new StringContent(loginJson, Encoding.UTF8, "application/json");
        var loginResult = await client.PostAsync("/api/auth/login", loginContent);

        // Verify login succeeded
        loginResult.StatusCode.Should().Be(HttpStatusCode.OK);
        var loginResponseContent = await loginResult.Content.ReadAsStringAsync();
        var parsedLoginResponse = JsonSerializer.Deserialize<AuthResponse>(loginResponseContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
        parsedLoginResponse!.AccessToken.Should().Be(initialAccessToken);

        // Step 2: Verify we can use the access token (simulate by checking token structure)
        // In integration tests, we verify the token is properly formatted
        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(initialAccessToken);

        jsonToken.Subject.Should().Be(userId);
        jsonToken.Claims.FirstOrDefault(c => c.Type == "email")?.Value.Should().Be(email);
        jsonToken.Claims.FirstOrDefault(c => c.Type == "aud")?.Value.Should().Be("authenticated");

        // Step 3: Refresh tokens
        var refreshedAccessToken = GenerateValidJwtToken(userId, email);
        var newRefreshToken = "new-refresh-token";

        var refreshResponse = new AuthResponse
        {
            AccessToken = refreshedAccessToken,
            RefreshToken = newRefreshToken,
            TokenType = "bearer",
            ExpiresIn = 3600
        };

        _supabaseClientMock
            .Setup(x => x.RefreshTokenAsync(It.Is<TokenRefreshRequest>(r => r.RefreshToken == initialRefreshToken)))
            .ReturnsAsync(refreshResponse);

        var refreshRequest = new TokenRefreshRequest { RefreshToken = initialRefreshToken };
        var refreshJson = JsonSerializer.Serialize(refreshRequest);
        var refreshContent = new StringContent(refreshJson, Encoding.UTF8, "application/json");
        var refreshResult = await client.PostAsync("/api/auth/token/refresh", refreshContent);

        // Verify refresh succeeded
        refreshResult.StatusCode.Should().Be(HttpStatusCode.OK);
        var refreshResponseContent = await refreshResult.Content.ReadAsStringAsync();
        var parsedRefreshResponse = JsonSerializer.Deserialize<AuthResponse>(refreshResponseContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
        parsedRefreshResponse!.AccessToken.Should().Be(refreshedAccessToken);

        // Step 4: Verify the refreshed token is also properly formatted
        var refreshedJsonToken = tokenHandler.ReadJwtToken(refreshedAccessToken);

        refreshedJsonToken.Subject.Should().Be(userId);
        refreshedJsonToken.Claims.FirstOrDefault(c => c.Type == "email")?.Value.Should().Be(email);
        refreshedJsonToken.Claims.FirstOrDefault(c => c.Type == "aud")?.Value.Should().Be("authenticated");

        // Verify all mocks were called as expected
        _supabaseClientMock.Verify(x => x.LoginAsync(It.IsAny<LoginRequest>()), Times.Once);
        _supabaseClientMock.Verify(x => x.RefreshTokenAsync(It.IsAny<TokenRefreshRequest>()), Times.Once);
    }

    [Fact]
    public void TokenValidation_GeneratedTokenStructure_ShouldBeValid()
    {
        // This test verifies that our generated tokens have the correct structure
        // and can be parsed correctly

        var userId = "structure-test-user";
        var email = "<EMAIL>";
        var validToken = GenerateValidJwtToken(userId, email);

        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(validToken);

        // Verify token structure
        jsonToken.Should().NotBeNull();
        jsonToken.Subject.Should().Be(userId);
        jsonToken.Issuer.Should().Be($"{TestSupabaseUrl}/auth/v1");
        jsonToken.Audiences.Should().Contain("authenticated");

        // Verify claims
        jsonToken.Claims.FirstOrDefault(c => c.Type == "email")?.Value.Should().Be(email);
        jsonToken.Claims.FirstOrDefault(c => c.Type == "aud")?.Value.Should().Be("authenticated");
        jsonToken.Claims.FirstOrDefault(c => c.Type == "role")?.Value.Should().Be("authenticated");
    }

    [Fact]
    public void TokenValidation_ExpiredTokenStructure_ShouldBeDetectable()
    {
        // This test verifies that expired tokens can be detected
        var userId = "expired-user";
        var email = "<EMAIL>";
        var expiredToken = GenerateExpiredJwtToken(userId, email);

        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(expiredToken);

        // Verify the token is structurally valid but expired
        jsonToken.Should().NotBeNull();
        jsonToken.Subject.Should().Be(userId);
        jsonToken.ValidTo.Should().BeBefore(DateTime.UtcNow); // Should be expired
    }

    private string GenerateValidJwtToken(string userId, string email)
    {
        return GenerateJwtToken(userId, email, DateTime.UtcNow.AddHours(1));
    }

    private string GenerateExpiredJwtToken(string userId, string email)
    {
        // Create a token that was issued 2 hours ago and expired 1 hour ago
        var expiredTime = DateTime.UtcNow.AddHours(-1);
        return GenerateJwtTokenWithCustomTimes(userId, email, DateTime.UtcNow.AddHours(-2), expiredTime);
    }

    private string GenerateJwtToken(string userId, string email, DateTime expiry)
    {
        var issuedAt = DateTime.UtcNow.AddMinutes(-5); // Issued 5 minutes ago
        return GenerateJwtTokenWithCustomTimes(userId, email, issuedAt, expiry);
    }

    private string GenerateJwtTokenWithCustomTimes(string userId, string email, DateTime issuedAt, DateTime expiry)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(TestJwtSecret);
        var signingKey = new SymmetricSecurityKey(key);
        var credentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

        var notBefore = issuedAt; // Valid from when it was issued

        var claims = new[]
        {
            new Claim("aud", "authenticated"),
            new Claim("exp", ((DateTimeOffset)expiry).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new Claim("iat", ((DateTimeOffset)issuedAt).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new Claim("nbf", ((DateTimeOffset)notBefore).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new Claim("sub", userId),
            new Claim("email", email),
            new Claim("role", "authenticated"),
            new Claim("aal", "aal1")
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = expiry,
            NotBefore = notBefore,
            IssuedAt = issuedAt,
            Issuer = $"{TestSupabaseUrl}/auth/v1",
            Audience = "authenticated",
            SigningCredentials = credentials
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }
}
