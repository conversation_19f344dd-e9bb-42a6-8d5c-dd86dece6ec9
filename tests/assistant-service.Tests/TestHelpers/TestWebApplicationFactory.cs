using AssistantService.Services;
using AssistantService.Data;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using System.Text.Encodings.Web;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;

namespace AssistantService.Tests.TestHelpers;

public class TestAuthenticationHandler(IOptionsMonitor<AuthenticationSchemeOptions> options,
    ILoggerFactory logger, UrlEncoder encoder) : AuthenticationHandler<AuthenticationSchemeOptions>(options, logger, encoder)
{
    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var claims = new[]
        {
            new Claim(ClaimTypes.Name, "Test User"),
            new Claim(ClaimTypes.NameIdentifier, "00000000-0000-0000-0000-000000000001"),
            new Claim("sub", "00000000-0000-0000-0000-000000000001")
        };

        var identity = new ClaimsIdentity(claims, "Test");
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, "Test");

        return Task.FromResult(AuthenticateResult.Success(ticket));
    }
}

public class TestWebApplicationFactory : WebApplicationFactory<Program>
{
    public Mock<IAIClient> MockAIClient { get; private set; } = null!;

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        // Set the environment early to ensure JWT configuration detects test environment
        builder.UseEnvironment("Testing");

        builder.ConfigureServices(services =>
        {
            // Remove the existing IAIClient registration
            var aiClientDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IAIClient));
            if (aiClientDescriptor != null)
                services.Remove(aiClientDescriptor);

            // Create a mock IAIClient directly instead of trying to mock HttpClient
            MockAIClient = new Mock<IAIClient>();

            // Set up default mock response for integration tests
            MockAIClient
                .Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AIProvider>()))
                .ReturnsAsync("Mocked response from Ollama");

            services.AddSingleton(MockAIClient.Object);

            // Remove existing database context registration (if any)
            var dbContextDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<AssistantDbContext>));
            if (dbContextDescriptor != null)
                services.Remove(dbContextDescriptor);

            var contextDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(AssistantDbContext));
            if (contextDescriptor != null)
                services.Remove(contextDescriptor);

            // Add in-memory database for testing
            services.AddDbContext<AssistantDbContext>(options =>
            {
                options.UseInMemoryDatabase("TestDb");
            });

            // Configure test authentication
            services.AddAuthentication("Test")
                .AddScheme<AuthenticationSchemeOptions, TestAuthenticationHandler>("Test", options => { });

            services.AddAuthorizationBuilder()
                .SetDefaultPolicy(new AuthorizationPolicyBuilder("Test")
                    .RequireAuthenticatedUser()
                    .Build());
        });

        // Configure test settings to disable security features
        builder.ConfigureAppConfiguration((context, config) =>
        {
            config.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Security:EnableRateLimiting"] = "false",
                ["Security:EnablePromptInjectionDetection"] = "false",
                ["Security:EnableContentFiltering"] = "false"
            });
        });
    }

    public void SetupMockResponse(string response)
    {
        if (MockAIClient == null)
            throw new InvalidOperationException("MockAIClient is not initialized");

        MockAIClient
            .Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AIProvider>()))
            .ReturnsAsync(response);
    }

    public void SetupMockException(Exception exception)
    {
        if (MockAIClient == null)
            throw new InvalidOperationException("MockAIClient is not initialized");

        MockAIClient
            .Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AIProvider>()))
            .ThrowsAsync(exception);
    }

    public void VerifyOllamaCall(Times times)
    {
        if (MockAIClient == null)
            throw new InvalidOperationException("MockAIClient is not initialized");

        MockAIClient
            .Verify(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AIProvider>()), times);
    }

    public void ResetMock()
    {
        MockAIClient?.Reset();
    }
}
