apiVersion: apps/v1
kind: Deployment
metadata:
  name: thread-service
  namespace: abraapi
  labels:
    app: thread-service
    service: thread
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: thread-service
  template:
    metadata:
      labels:
        app: thread-service
        service: thread
        version: v1
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: thread-service
        image: ghcr.io/arnyfesto1/abraapp-thread-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: thread-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: thread-service-config
              key: ASPNETCORE_URLS

        - name: SUPABASE_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_CONNECTION_STRING

        - name: S<PERSON><PERSON>ASE_URL
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_URL

        - name: SUPABASE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_PROJECT_ID
        
        - name: SUPABASE_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_JWT_SECRET

        - name: SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_ANON_KEY

        - name: SUPABASE_SERVICE_ROLE_KEY
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_SERVICE_ROLE_KEY

        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 120
          periodSeconds: 15
          timeoutSeconds: 30
          failureThreshold: 20
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 180
          periodSeconds: 45
          timeoutSeconds: 30
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 15
          timeoutSeconds: 30
          failureThreshold: 3
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      imagePullSecrets:
        - name: ghcr-creds
