using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.Collections.Concurrent;

namespace ThreadService.Services;

/// <summary>
/// Custom JWT configuration for Supabase that uses background service for JWKS cache management
/// </summary>
public class SupabaseJwtConfiguration : IConfigureNamedOptions<JwtBearerOptions>
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<SupabaseJwtConfiguration> _logger;
    private readonly ConcurrentDictionary<string, SecurityKey> _keyCache;

    public SupabaseJwtConfiguration(
        IConfiguration configuration,
        ILogger<SupabaseJwtConfiguration> logger,
        JwksKeyCache keyCache)
    {
        _configuration = configuration;
        _logger = logger;
        _keyCache = keyCache.Keys;
    }

    public void Configure(string? name, JwtBearerOptions options)
    {
        if (name != JwtBearerDefaults.AuthenticationScheme)
            return;

        Configure(options);
    }

    public void Configure(JwtBearerOptions options)
    {
        var supabaseUrl = _configuration["SUPABASE_URL"];
        var supabaseProjectId = _configuration["SUPABASE_PROJECT_ID"];

        // Validate required configuration
        if (string.IsNullOrEmpty(supabaseUrl))
        {
            var message = "SUPABASE_URL configuration is required for JWT validation.";
            _logger.LogError("SUPABASE_URL configuration is required for JWT validation");
            throw new InvalidOperationException(message);
        }

        if (string.IsNullOrEmpty(supabaseProjectId))
        {
            var message = "SUPABASE_PROJECT_ID configuration is required for JWT validation.";
            _logger.LogError("SUPABASE_PROJECT_ID configuration is required for JWT validation");
            throw new InvalidOperationException(message);
        }

        // For JWKS requests, prefer anon key but allow service role key as fallback
        // Note: Service role key has elevated privileges - use with caution
        var supabaseApiKey = _configuration["SUPABASE_ANON_KEY"];
        if (string.IsNullOrEmpty(supabaseApiKey))
        {
            supabaseApiKey = _configuration["SUPABASE_SERVICE_ROLE_KEY"];
            if (!string.IsNullOrEmpty(supabaseApiKey))
            {
                _logger.LogWarning("Using SUPABASE_SERVICE_ROLE_KEY for JWKS requests. Consider using SUPABASE_ANON_KEY instead for security.");
            }
        }

        _logger.LogInformation("Configuring Supabase JWT validation for Thread service - URL: {SupabaseUrl}", supabaseUrl);

        // Configure basic JWT options
        options.Authority = $"{supabaseUrl}/auth/v1";
        options.RequireHttpsMetadata = !IsTestEnvironment();
        options.SaveToken = true;

        // Configure token validation parameters
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = $"{supabaseUrl}/auth/v1",
            ValidateAudience = true,
            ValidAudiences = ["authenticated", supabaseProjectId],
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            RequireSignedTokens = true,
            RequireExpirationTime = true,
            ClockSkew = TimeSpan.FromSeconds(30),

            // Disable automatic claim name mapping to preserve original JWT claim names
            NameClaimType = "sub",
            RoleClaimType = "role",

            // Custom key resolver that handles Supabase JWKS with API key
            IssuerSigningKeyResolver = (token, securityToken, kid, validationParameters) =>
            {
                return ResolveSigningKey(kid, supabaseUrl, supabaseApiKey);
            }
        };

        // Disable default claim name mapping to preserve original JWT claims
        options.MapInboundClaims = false;

        // Enhanced event handlers for debugging
        options.Events = new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                LogAuthenticationFailure(context);
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                LogTokenValidationSuccess(context);
                return Task.CompletedTask;
            },
            OnMessageReceived = context =>
            {
                LogTokenReceived(context);
                return Task.CompletedTask;
            }
        };
    }

    private IEnumerable<SecurityKey> ResolveSigningKey(string? kid, string supabaseUrl, string? apiKey)
    {
        if (string.IsNullOrEmpty(kid))
        {
            _logger.LogWarning("Token has no key ID (kid), cannot resolve signing key");
            return [];
        }

        // Check cache only - background service manages the cache asynchronously
        if (_keyCache.TryGetValue(kid, out var cachedKey))
        {
            _logger.LogDebug("Using cached signing key for kid: {KeyId}", kid);
            return [cachedKey];
        }

        _logger.LogWarning("Signing key with kid {KeyId} not found in cache. Background service may still be loading keys.", kid);
        return [];
    }



    private void LogAuthenticationFailure(AuthenticationFailedContext context)
    {
        var exceptionType = context.Exception.GetType().Name;
        _logger.LogError("JWT Authentication failed in Thread service: {Error} ({ExceptionType})",
            context.Exception.Message, exceptionType);

        // Specific error handling
        switch (context.Exception)
        {
            case SecurityTokenInvalidAudienceException:
                _logger.LogError("Invalid audience. Expected: authenticated or project ID");
                break;
            case SecurityTokenInvalidIssuerException:
                _logger.LogError("Invalid issuer. Expected: {ExpectedIssuer}",
                    $"{_configuration["SUPABASE_URL"]}/auth/v1");
                break;
            case SecurityTokenSignatureKeyNotFoundException:
                _logger.LogError("Signing key not found. Check JWKS endpoint accessibility");
                break;
            case SecurityTokenInvalidSignatureException:
                _logger.LogError("Invalid token signature. Check token integrity");

                // Extract kid from token header for debugging
                try
                {
                    if (context.HttpContext.Request.Headers.TryGetValue("Authorization", out var authHeader))
                    {
                        var token = authHeader.ToString().Replace("Bearer ", "");
                        if (!string.IsNullOrEmpty(token))
                        {
                            var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
                            if (handler.CanReadToken(token))
                            {
                                var jwtToken = handler.ReadJwtToken(token);
                                var kid = jwtToken.Header.Kid;
                                _logger.LogError("Token kid (Key ID): {Kid}", kid ?? "null");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not extract kid from token for debugging");
                }
                break;
        }
    }

    private void LogTokenValidationSuccess(TokenValidatedContext context)
    {
        var userId = context.Principal?.FindFirst("sub")?.Value;
        var email = context.Principal?.FindFirst("email")?.Value;
        var role = context.Principal?.FindFirst("role")?.Value;
        var audience = context.Principal?.FindFirst("aud")?.Value;

        // Redact email for privacy - only show first 3 chars + domain
        var redactedEmail = RedactEmail(email);

        _logger.LogInformation("JWT validation successful in Thread service - User: {UserId}, Email: {Email}, Role: {Role}, Audience: {Audience}",
            userId, redactedEmail, role, audience);

        // Log full details at Debug level for development/troubleshooting
        _logger.LogDebug("JWT validation details - Full Email: {FullEmail}", email);
    }

    private static string? RedactEmail(string? email)
    {
        if (string.IsNullOrEmpty(email))
            return null;

        var atIndex = email.IndexOf('@');
        if (atIndex <= 0)
            return "***"; // Invalid email format

        var localPart = email[..atIndex];
        var domain = email[atIndex..];

        // Show first 3 characters of local part, then *** + domain
        var visibleChars = Math.Min(3, localPart.Length);
        return string.Concat(localPart.AsSpan(0, visibleChars), "***", domain);
    }

    private void LogTokenReceived(MessageReceivedContext context)
    {
        if (!string.IsNullOrEmpty(context.Token))
        {
            _logger.LogDebug("JWT token received and being processed in Thread service");
        }
    }

    private bool IsTestEnvironment()
    {
        var environment = _configuration["ASPNETCORE_ENVIRONMENT"];
        return string.Equals(environment, "Testing", StringComparison.OrdinalIgnoreCase) ||
               string.Equals(environment, "Test", StringComparison.OrdinalIgnoreCase);
    }
}
