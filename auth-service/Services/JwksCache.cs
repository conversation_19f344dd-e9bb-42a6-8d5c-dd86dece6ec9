using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;

namespace AuthService.Services;

/// <summary>
/// Thread-safe cache for storing JWKS keys
/// </summary>
public class JwksCache
{
    private readonly ConcurrentDictionary<string, SecurityKey> _keys = new();
    private readonly ILogger<JwksCache> _logger;
    private readonly object _updateLock = new();
    private DateTimeOffset _lastUpdated = DateTimeOffset.MinValue;

    public JwksCache(ILogger<JwksCache> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Gets a signing key by its Key ID (kid) synchronously
    /// </summary>
    /// <param name="kid">The Key ID to look for</param>
    /// <returns>The SecurityKey if found, null otherwise</returns>
    public SecurityKey? GetSigningKey(string kid)
    {
        if (_keys.TryGetValue(kid, out var key))
        {
            return key;
        }

        _logger.LogWarning("Signing key with ID {KeyId} not found in cache", kid);
        return null;
    }

    /// <summary>
    /// Updates the cache with new keys in a thread-safe manner
    /// </summary>
    /// <param name="newKeys">The new keys to store in the cache</param>
    public void UpdateKeys(ConcurrentDictionary<string, SecurityKey> newKeys)
    {
        // Use a lock to ensure atomic updates
        lock (_updateLock)
        {
            // Clear the existing keys
            _keys.Clear();

            // Add all the new keys
            foreach (var (kid, key) in newKeys)
            {
                _keys[kid] = key;
            }

            _lastUpdated = DateTimeOffset.UtcNow;
            _logger.LogInformation("JWKS cache updated with {Count} keys at {Time}", newKeys.Count, _lastUpdated);
        }
    }

    /// <summary>
    /// Gets all keys currently in the cache
    /// </summary>
    public IReadOnlyDictionary<string, SecurityKey> GetAllKeys()
    {
        return _keys;
    }

    /// <summary>
    /// Gets the time when the cache was last updated
    /// </summary>
    public DateTimeOffset LastUpdated => _lastUpdated;

    /// <summary>
    /// Checks if the cache is empty
    /// </summary>
    public bool IsEmpty => _keys.IsEmpty;

    /// <summary>
    /// Gets the number of keys in the cache
    /// </summary>
    public int Count => _keys.Count;
}