using System.Collections.Concurrent;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;

namespace AuthService.Services;

/// <summary>
/// Service for fetching and caching JSON Web Keys (JWK) from a JWKS endpoint
/// </summary>
public class JwksService
{
    private readonly HttpClient _httpClient;
    private readonly IMemoryCache _cache;
    private readonly ILogger<JwksService> _logger;
    private readonly string _jwksUri;
    private readonly TimeSpan _cacheExpiration;
    private const string CACHE_KEY = "JWKS_KEYS";
    private readonly JwksCache? _jwksCache;
    private readonly SemaphoreSlim _refreshLock = new(1, 1);
public JwksService(
    HttpClient httpClient,
    IMemoryCache cache,
    ILogger<JwksService> logger,
    string jwksUri,
    TimeSpan? cacheExpiration = null,
    JwksCache? jwksCache = null)
{
    if (string.IsNullOrWhiteSpace(jwksUri))
        throw new ArgumentException("JWKS URI cannot be null or empty", nameof(jwksUri));

    if (!Uri.IsWellFormedUriString(jwksUri, UriKind.Absolute))
        throw new ArgumentException("JWKS URI must be a valid absolute URI", nameof(jwksUri));

    _httpClient = httpClient;
    _cache = cache;
    _logger = logger;
    _jwksUri = jwksUri;
    _cacheExpiration = cacheExpiration ?? TimeSpan.FromHours(24); // Default to 24 hours
    _jwksCache = jwksCache;
}
    public async Task<SecurityKey?> GetSigningKeyAsync(string kid)
    {
        try
        {
            // If JwksCache is available, use it first
            if (_jwksCache != null && !_jwksCache.IsEmpty)
            {
                var cachedKey = _jwksCache.GetSigningKey(kid);
                if (cachedKey != null)
                {
                    return cachedKey;
                }
            }
            
            // Otherwise use the original implementation
            var keys = await GetJwksAsync();
            
            if (keys.TryGetValue(kid, out var key))
            {
                return key;
            }
            
            // If key not found in cache, force refresh and try again
            keys = await GetJwksAsync(forceRefresh: true);
            
            if (keys.TryGetValue(kid, out key))
            {
                return key;
            }
            
            _logger.LogWarning("Signing key with ID {KeyId} not found in JWKS", kid);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting signing key with ID {KeyId}", kid);
            return null;
        }
    }

    /// <summary>
    /// Gets a signing key by its Key ID (kid) synchronously
    /// </summary>
    /// <param name="kid">The Key ID to look for</param>
    /// <returns>The SecurityKey if found, null otherwise</returns>
    public SecurityKey? GetSigningKey(string kid)
    {
        try
        {
            _logger.LogDebug("GetSigningKey called for key ID: {KeyId}", kid);
            
            // If JwksCache is available, use it first
            if (_jwksCache != null && !_jwksCache.IsEmpty)
            {
                _logger.LogDebug("Checking JwksCache for key ID: {KeyId}", kid);
                var cachedKey = _jwksCache.GetSigningKey(kid);
                if (cachedKey != null)
                {
                    _logger.LogDebug("Found key ID {KeyId} in JwksCache", kid);
                    return cachedKey;
                }
                _logger.LogDebug("Key ID {KeyId} not found in JwksCache", kid);
            }
            else
            {
                _logger.LogWarning("JwksCache is null or empty when looking for key ID: {KeyId}", kid);
            }
            
            // For synchronous access, we need to get the keys synchronously
            // This will block, but it's necessary for JWT validation which requires synchronous access
            _logger.LogDebug("Fetching keys from JWKS endpoint for key ID: {KeyId}", kid);
            var keys = GetJwksAsync().GetAwaiter().GetResult();
            
            if (keys.TryGetValue(kid, out var key))
            {
                _logger.LogDebug("Found key ID {KeyId} in JWKS response", kid);
                return key;
            }
            
            // If key not found in cache, force refresh and try again
            _logger.LogDebug("Key ID {KeyId} not found in JWKS response, forcing refresh", kid);
            keys = GetJwksAsync(forceRefresh: true).GetAwaiter().GetResult();
            
            if (keys.TryGetValue(kid, out key))
            {
                _logger.LogDebug("Found key ID {KeyId} in forced JWKS refresh", kid);
                return key;
            }
            
            _logger.LogWarning("Signing key with ID {KeyId} not found in JWKS after forced refresh", kid);
            
            // Log all available keys for debugging
            if (keys.Count > 0)
            {
                _logger.LogInformation("Available keys in JWKS: {Keys}", string.Join(", ", keys.Keys));
            }
            else
            {
                _logger.LogWarning("No keys available in JWKS");
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting signing key with ID {KeyId}", kid);
            return null;
        }
    }

    /// <summary>
    /// Gets all JSON Web Keys from the JWKS endpoint
    /// </summary>
    /// <param name="forceRefresh">Whether to force a refresh from the JWKS endpoint</param>
    /// <returns>Dictionary of Key IDs to SecurityKeys</returns>
    public async Task<ConcurrentDictionary<string, SecurityKey>> GetJwksAsync(bool forceRefresh = false)
    {
        // Check if we can use cached keys
        if (!forceRefresh && _cache.TryGetValue(CACHE_KEY, out ConcurrentDictionary<string, SecurityKey>? cachedKeys) && cachedKeys != null)
        {
            _logger.LogDebug("Using cached JWKS keys, count: {Count}", cachedKeys.Count);
            return cachedKeys;
        }

        try
        {
            _logger.LogInformation("Fetching JWKS from {JwksUri}", _jwksUri);
            
            // Set a timeout for the HTTP request
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            
            // Make the HTTP request with timeout
            var response = await _httpClient.GetAsync(_jwksUri, cts.Token);
            
            // Log the response status code
            _logger.LogDebug("JWKS response status code: {StatusCode}", response.StatusCode);
            
            // Ensure we got a successful response
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            
            // Log the raw JSON for debugging (truncated for large responses)
            if (json.Length > 1000)
            {
                _logger.LogDebug("JWKS Response (truncated): {Json}...", json.Substring(0, 1000));
            }
            else
            {
                _logger.LogDebug("JWKS Response: {Json}", json);
            }
            
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            
            var jwks = JsonSerializer.Deserialize<JsonWebKeySet>(json, options);
            
            if (jwks?.Keys == null || jwks.Keys.Count == 0)
            {
                _logger.LogWarning("No keys found in JWKS response from {JwksUri}", _jwksUri);
                return new ConcurrentDictionary<string, SecurityKey>();
            }

            _logger.LogInformation("Found {Count} keys in JWKS response", jwks.Keys.Count);
            
            var keys = new ConcurrentDictionary<string, SecurityKey>();
            
            foreach (var jwk in jwks.Keys)
            {
                if (string.IsNullOrEmpty(jwk.Kid))
                {
                    _logger.LogWarning("Skipping JWK with no Key ID");
                    continue;
                }

                try
                {
                    SecurityKey securityKey;
                    
                    // Log key details for debugging
                    _logger.LogDebug("Processing key: ID={KeyId}, Type={KeyType}, Algorithm={Algorithm}, Curve={Curve}",
                        jwk.Kid, jwk.Kty, jwk.Alg, jwk.Crv);
                    
                    // Handle different key types
                    if (jwk.Kty == "EC" && jwk.Crv == "P-256")
                    {
                        // ES256 keys
                        _logger.LogDebug("Creating EC key with curve P-256");
                        var ecDsa = CreateECDsaFromJwk(jwk);
                        securityKey = new ECDsaSecurityKey(ecDsa)
                        {
                            KeyId = jwk.Kid
                        };
                    }
                    else if (jwk.Kty == "RSA")
                    {
                        // RSA keys
                        _logger.LogDebug("Creating RSA key");
                        var rsa = CreateRSAFromJwk(jwk);
                        securityKey = new RsaSecurityKey(rsa)
                        {
                            KeyId = jwk.Kid
                        };
                    }
                    else
                    {
                        _logger.LogWarning("Unsupported key type: {KeyType}, curve: {Curve}", jwk.Kty, jwk.Crv);
                        continue;
                    }
                    
                    keys[jwk.Kid] = securityKey;
                    _logger.LogInformation("Added key with ID {KeyId} and type {KeyType}", jwk.Kid, jwk.Kty);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing JWK with ID {KeyId}", jwk.Kid);
                }
            }

            // Cache the keys
            _cache.Set(CACHE_KEY, keys, _cacheExpiration);
            
            // Also update the JwksCache if available
            if (_jwksCache != null)
            {
                _logger.LogDebug("Updating JwksCache with {Count} keys", keys.Count);
                _jwksCache.UpdateKeys(keys);
            }
            else
            {
                _logger.LogWarning("JwksCache is null, cannot update with new keys");
            }
            
            return keys;
        }
        catch (TaskCanceledException)
        {
            _logger.LogError("Timeout while fetching JWKS from {JwksUri}", _jwksUri);
            return new ConcurrentDictionary<string, SecurityKey>();
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error fetching JWKS from {JwksUri}: {Message}", _jwksUri, ex.Message);
            return new ConcurrentDictionary<string, SecurityKey>();
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Error parsing JWKS response from {JwksUri}: {Message}", _jwksUri, ex.Message);
            return new ConcurrentDictionary<string, SecurityKey>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error fetching JWKS from {JwksUri}: {Message}", _jwksUri, ex.Message);
            return new ConcurrentDictionary<string, SecurityKey>();
        }
    }

    /// <summary>
    /// Creates an ECDsa instance from a JWK
    /// </summary>
    /// <param name="jwk">The JSON Web Key containing EC parameters</param>
    /// <returns>An initialized ECDsa instance</returns>
    /// <exception cref="ArgumentNullException">Thrown when jwk is null</exception>
    /// <exception cref="ArgumentException">Thrown when required JWK properties are missing or invalid</exception>
    private static ECDsa CreateECDsaFromJwk(JsonWebKey jwk)
    {
        // Validate input JWK
        if (jwk == null)
        {
            throw new ArgumentNullException(nameof(jwk), "JWK cannot be null");
        }

        if (string.IsNullOrEmpty(jwk.X))
        {
            throw new ArgumentException("JWK X parameter is required", nameof(jwk));
        }

        if (string.IsNullOrEmpty(jwk.Y))
        {
            throw new ArgumentException("JWK Y parameter is required", nameof(jwk));
        }

        if (string.IsNullOrEmpty(jwk.Crv))
        {
            throw new ArgumentException("JWK Crv parameter is required", nameof(jwk));
        }

        // Select the appropriate curve based on the JWK curve parameter
        ECCurve curve = jwk.Crv switch
        {
            "P-256" => ECCurve.NamedCurves.nistP256,
            "P-384" => ECCurve.NamedCurves.nistP384,
            "P-521" => ECCurve.NamedCurves.nistP521,
            _ => throw new ArgumentException($"Unsupported curve: {jwk.Crv}", nameof(jwk))
        };

        var ecDsa = ECDsa.Create();
        var parameters = new ECParameters
        {
            Curve = curve,
            Q = new ECPoint
            {
                X = Base64UrlDecode(jwk.X),
                Y = Base64UrlDecode(jwk.Y)
            }
        };
        
        ecDsa.ImportParameters(parameters);
        return ecDsa;
    }

    /// <summary>
    /// Creates an RSA instance from a JWK
    /// </summary>
    /// <param name="jwk">The JSON Web Key containing RSA parameters</param>
    /// <returns>An initialized RSA instance</returns>
    /// <exception cref="ArgumentNullException">Thrown when jwk is null</exception>
    /// <exception cref="ArgumentException">Thrown when required JWK properties are missing or invalid</exception>
    private static RSA CreateRSAFromJwk(JsonWebKey jwk)
    {
        // Validate input JWK
        if (jwk == null)
        {
            throw new ArgumentNullException(nameof(jwk), "JWK cannot be null");
        }

        if (string.IsNullOrEmpty(jwk.N))
        {
            throw new ArgumentException("JWK N (modulus) parameter is required", nameof(jwk));
        }

        if (string.IsNullOrEmpty(jwk.E))
        {
            throw new ArgumentException("JWK E (exponent) parameter is required", nameof(jwk));
        }

        var rsa = RSA.Create();
        var parameters = new RSAParameters
        {
            Modulus = Base64UrlDecode(jwk.N),
            Exponent = Base64UrlDecode(jwk.E)
        };
        
        rsa.ImportParameters(parameters);
        return rsa;
    }

    /// <summary>
    /// Decodes a Base64Url encoded string
    /// </summary>
    private static byte[] Base64UrlDecode(string input)
    {
        string padded = input.Length % 4 == 0
            ? input
            : input + new string('=', 4 - input.Length % 4);
            
        string base64 = padded
            .Replace('-', '+')
            .Replace('_', '/');
            
        return Convert.FromBase64String(base64);
    }
}

/// <summary>
/// Represents a JSON Web Key Set
/// </summary>
public class JsonWebKeySet
{
    [JsonPropertyName("keys")]
    public List<JsonWebKey> Keys { get; set; } = new();
}

/// <summary>
/// Represents a JSON Web Key
/// </summary>
public class JsonWebKey
{
    [JsonPropertyName("kid")]
    public string Kid { get; set; } = string.Empty;
    
    [JsonPropertyName("kty")]
    public string Kty { get; set; } = string.Empty;
    
    [JsonPropertyName("use")]
    public string Use { get; set; } = string.Empty;
    
    [JsonPropertyName("alg")]
    public string Alg { get; set; } = string.Empty;
    
    [JsonPropertyName("crv")]
    public string Crv { get; set; } = string.Empty;
    
    [JsonPropertyName("x")]
    public string X { get; set; } = string.Empty;
    
    [JsonPropertyName("y")]
    public string Y { get; set; } = string.Empty;
    
    [JsonPropertyName("n")]
    public string N { get; set; } = string.Empty;
    
    [JsonPropertyName("e")]
    public string E { get; set; } = string.Empty;
}