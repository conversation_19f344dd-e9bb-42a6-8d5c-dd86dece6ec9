using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using Microsoft.IdentityModel.Tokens;
using Polly;
using Polly.Retry;

namespace AuthService.Services;

/// <summary>
/// Background service that periodically fetches J<PERSON><PERSON> keys and updates the cache
/// </summary>
public class JwksBackgroundService : BackgroundService
{
    private readonly JwksService _jwksService;
    private readonly JwksCache _jwksCache;
    private readonly ILogger<JwksBackgroundService> _logger;
    private readonly TimeSpan _refreshInterval;
    private readonly AsyncRetryPolicy _retryPolicy;

    public JwksBackgroundService(
        JwksService jwksService,
        JwksCache jwksCache,
        ILogger<JwksBackgroundService> logger,
        TimeSpan? refreshInterval = null)
    {
        _jwksService = jwksService;
        _jwksCache = jwksCache;
        _logger = logger;
        _refreshInterval = refreshInterval ?? TimeSpan.FromHours(1); // Default to 1 hour

        // Create a retry policy for fetching JWKS keys
        _retryPolicy = Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(
                3, // Number of retries
                retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // Exponential backoff
                (exception, timeSpan, retryCount, context) =>
                {
                    _logger.LogWarning(exception, 
                        "Error fetching JWKS keys (attempt {RetryCount}). Retrying in {RetryTimeSpan}...", 
                        retryCount, timeSpan);
                }
            );
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("JWKS background service started. Will refresh keys every {RefreshInterval}", _refreshInterval);

        // Immediately fetch keys on startup
        await RefreshKeysWithRetryAsync(stoppingToken);

        // Then periodically refresh
        using var timer = new PeriodicTimer(_refreshInterval);
        
        while (!stoppingToken.IsCancellationRequested && await timer.WaitForNextTickAsync(stoppingToken))
        {
            await RefreshKeysWithRetryAsync(stoppingToken);
        }
    }

    private async Task RefreshKeysWithRetryAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Log the current state of the cache before refresh
            _logger.LogInformation("Current JWKS cache state before refresh: IsEmpty={IsEmpty}, KeyCount={KeyCount}, LastUpdated={LastUpdated}",
                _jwksCache.IsEmpty, _jwksCache.Count, _jwksCache.LastUpdated);
            
            await _retryPolicy.ExecuteAsync(async () =>
            {
                _logger.LogInformation("Fetching JWKS keys from remote endpoint");
                
                // Force refresh to get the latest keys
                ConcurrentDictionary<string, SecurityKey> keys = await _jwksService.GetJwksAsync(forceRefresh: true);
                
                if (keys.Count == 0)
                {
                    _logger.LogWarning("No JWKS keys returned from endpoint, throwing exception to trigger retry");
                    throw new InvalidOperationException("No JWKS keys returned from endpoint");
                }
                
                // Log the keys we're about to add to the cache
                _logger.LogDebug("Retrieved {Count} keys from JWKS endpoint: {Keys}",
                    keys.Count, string.Join(", ", keys.Keys));
                
                // Update the cache with the new keys
                _jwksCache.UpdateKeys(keys);
                
                _logger.LogInformation("Successfully updated JWKS cache with {Count} keys", keys.Count);
                
                // Return the keys to indicate success
                return keys;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh JWKS keys after multiple retries");
            
            // If the cache is empty, this is critical - we can't validate tokens
            if (_jwksCache.IsEmpty)
            {
                _logger.LogCritical("JWKS cache is empty! Token validation will fail until keys can be fetched.");
                // Log additional diagnostic information without exposing sensitive URIs
                _logger.LogCritical("JWT validation will fail for all requests until JWKS keys can be successfully fetched");
            }
            else
            {
                // Even if we failed to refresh, we still have some keys in the cache
                _logger.LogWarning("Failed to refresh JWKS keys, but cache is not empty. Using {Count} existing keys.", _jwksCache.Count);
            }
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("JWKS background service is stopping");
        await base.StopAsync(cancellationToken);
    }
}