using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using AuthService.Models;
using AuthService.Services;

namespace AuthService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController(ISupabaseClient supabase) : ControllerBase
{
    private readonly ISupabaseClient _supabase = supabase;

    // Authentication Endpoints
    [HttpPost("signup")]
    public async Task<IActionResult> Signup([FromBody] SignupRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Email) || string.IsNullOrWhiteSpace(request.Password))
            {
                return BadRequest("Email and password are required");
            }

            var result = await _supabase.SignupAsync(request);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Email) || string.IsNullOrWhiteSpace(request.Password))
            {
                return BadRequest("Email and password are required");
            }

            var result = await _supabase.LoginAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleAuthenticationError(ex, "login");
        }
    }

    [HttpPost("token/refresh")]
    public async Task<IActionResult> RefreshToken([FromBody] TokenRefreshRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.RefreshToken))
            {
                return BadRequest("Refresh token is required");
            }

            var result = await _supabase.RefreshTokenAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleAuthenticationError(ex, "token refresh");
        }
    }

    [HttpPost("password/recover")]
    public async Task<IActionResult> RecoverPassword([FromBody] PasswordRecoveryRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Email))
            {
                return BadRequest("Email is required");
            }

            var result = await _supabase.RecoverPasswordAsync(request);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    [HttpPost("otp")]
    public async Task<IActionResult> SendOtp([FromBody] OtpRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Email) && string.IsNullOrWhiteSpace(request.Phone))
            {
                return BadRequest("Either email or phone is required");
            }

            var result = await _supabase.SendOtpAsync(request);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    [HttpPost("verify")]
    public async Task<IActionResult> VerifyOtp([FromBody] VerifyRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Token) || string.IsNullOrWhiteSpace(request.Type))
            {
                return BadRequest("Token and type are required");
            }

            var result = await _supabase.VerifyOtpAsync(request);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    // User Management Endpoints
    [HttpGet("user")]
    [Authorize]
    public async Task<IActionResult> GetUser()
    {
        try
        {
            var accessToken = GetAccessTokenFromHeader();
            if (string.IsNullOrWhiteSpace(accessToken))
            {
                return Unauthorized("Access token is required");
            }

            var result = await _supabase.GetUserAsync(accessToken);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    [HttpPut("user/update")]
    [Authorize]
    public async Task<IActionResult> UpdateUser([FromBody] UpdateUserRequest request)
    {
        try
        {
            var accessToken = GetAccessTokenFromHeader();
            if (string.IsNullOrWhiteSpace(accessToken))
            {
                return Unauthorized("Access token is required");
            }

            var result = await _supabase.UpdateUserAsync(accessToken, request);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    [HttpPost("logout")]
    [Authorize]
    public async Task<IActionResult> Logout()
    {
        try
        {
            var accessToken = GetAccessTokenFromHeader();
            if (string.IsNullOrWhiteSpace(accessToken))
            {
                return Unauthorized("Access token is required");
            }

            var result = await _supabase.LogoutAsync(accessToken);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    // OAuth Endpoints
    [HttpPost("oauth")]
    public async Task<IActionResult> GetOAuthUrl([FromBody] OAuthRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Provider))
            {
                return BadRequest("Provider is required");
            }

            var result = await _supabase.GetOAuthUrlAsync(request);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    [HttpPost("oauth/callback")]
    public async Task<IActionResult> OAuthCallback([FromQuery] string code, [FromQuery] string provider)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(code) || string.IsNullOrWhiteSpace(provider))
            {
                return BadRequest("Code and provider are required");
            }

            var result = await _supabase.ExchangeCodeForTokenAsync(code, provider);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    // Google Sign-In Endpoints
    [HttpPost("google/signin")]
    public async Task<IActionResult> GetGoogleSignInUrl([FromBody] GoogleSignInRequest? request = null)
    {
        try
        {
            var result = await _supabase.GetGoogleSignInUrlAsync(request?.RedirectTo);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    [HttpPost("google/callback")]
    public async Task<IActionResult> GoogleCallback([FromBody] GoogleCallbackRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Code))
            {
                return BadRequest("Authorization code is required");
            }

            var result = await _supabase.SignInWithGoogleAsync(request.Code);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    // Phone Sign-In Endpoints (Twilio)
    [HttpPost("phone/send-otp")]
    public async Task<IActionResult> SendPhoneOtp([FromBody] PhoneOtpRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.PhoneNumber))
            {
                return BadRequest("Phone number is required");
            }

            var result = await _supabase.SendPhoneOtpAsync(request.PhoneNumber, request.CreateUser);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    [HttpPost("phone/signin")]
    public async Task<IActionResult> SignInWithPhone([FromBody] PhoneSignInRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.PhoneNumber) || string.IsNullOrWhiteSpace(request.OtpCode))
            {
                return BadRequest("Phone number and OTP code are required");
            }

            var result = await _supabase.SignInWithPhoneAsync(request.PhoneNumber, request.OtpCode);
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    // Settings Endpoint
    [HttpGet("settings")]
    public async Task<IActionResult> GetSettings()
    {
        try
        {
            var result = await _supabase.GetSettingsAsync();
            return Ok(result);
        }
        catch (HttpRequestException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    // Profile Management (using Supabase managed auth)
    [HttpGet("profile")]
    [Authorize]
    public async Task<IActionResult> GetProfile()
    {
        var userId = User.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;
        if (string.IsNullOrWhiteSpace(userId)) return Unauthorized();

        var user = await _supabase.GetProfileAsync(userId);
        if (user == null) return NotFound("User not found");

        // Return user profile information from Supabase auth
        var profile = new
        {
            user_id = user.Id,
            email = user.Email,
            phone = user.Phone,
            full_name = user.UserMetadata?.GetValueOrDefault("full_name")?.ToString(),
            username = user.UserMetadata?.GetValueOrDefault("username")?.ToString(),
            broker_id = user.UserMetadata?.GetValueOrDefault("broker_id")?.ToString(),
            verified = user.EmailConfirmedAt != null,
            created_at = user.CreatedAt
        };

        return Ok(profile);
    }

    [HttpPost("profile/link-broker")]
    [Authorize]
    public async Task<IActionResult> LinkBroker([FromBody] LinkBrokerRequest request)
    {
        try
        {
            var userId = User.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;
            if (string.IsNullOrWhiteSpace(userId)) return Unauthorized();

            if (string.IsNullOrWhiteSpace(request.BrokerId))
            {
                return BadRequest("Broker ID is required");
            }

            var result = await _supabase.LinkBrokerAsync(userId, request.BrokerId);
            if (result)
            {
                return Ok(new { message = "Broker linked successfully" });
            }
            else
            {
                return BadRequest(new { error = "Failed to link broker" });
            }
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    // Test endpoint for JWT validation
    [HttpGet("test-jwt")]
    [Authorize]
    public IActionResult TestJwtValidation()
    {
        // Extract user ID from nameidentifier or sub claim
        var userId = User.Claims.FirstOrDefault(c => c.Type == "sub")?.Value ??
                    User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier")?.Value;
        
        // Extract email from email or emailaddress claim
        var email = User.Claims.FirstOrDefault(c => c.Type == "email")?.Value ??
                   User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress")?.Value;
        
        // Get algorithm from HttpContext.Items (set in JwtBearer events)
        var tokenAlg = HttpContext.Items["jwt_algorithm"]?.ToString() ?? "ES256";
        
        return Ok(new
        {
            message = "JWT validation successful",
            user_id = userId,
            email = email,
            algorithm = tokenAlg,
            claims = User.Claims.Select(c => new { type = c.Type, value = c.Value }).ToList()
        });
    }

    // JWKS Cache Management
    [HttpGet("jwks-status")]
    public IActionResult GetJwksStatus([FromServices] JwksCache jwksCache)
    {
        var isHealthy = !jwksCache.IsEmpty && jwksCache.Count > 0;
        
        return Ok(new
        {
            status = isHealthy ? "Healthy" : "Unhealthy",
            cacheLastUpdated = jwksCache.LastUpdated.ToString("o"),
            keyCount = jwksCache.Count,
            isEmpty = jwksCache.IsEmpty,
            keys = jwksCache.GetAllKeys().Select(k => new
            {
                keyId = k.Key,
                keyType = k.Value.GetType().Name
            }).ToList()
        });
    }
    
    [HttpGet("jwks-refresh")]
    [Authorize(Roles = "admin")]
    public async Task<IActionResult> RefreshJwksCache([FromServices] JwksService jwksService, [FromServices] JwksCache jwksCache)
    {
        try
        {
            // Get the current state of the cache
            var beforeCount = jwksCache.Count;
            var beforeLastUpdated = jwksCache.LastUpdated;
            
            // Force refresh the JWKS cache
            var keys = await jwksService.GetJwksAsync(forceRefresh: true);
            
            // Check if we got any keys
            if (keys.Count == 0)
            {
                return StatusCode(500, new { error = "Failed to fetch JWKS keys", message = "No keys returned from JWKS endpoint" });
            }
            
            // Return the result
            return Ok(new
            {
                message = "JWKS cache refreshed successfully",
                before = new
                {
                    count = beforeCount,
                    lastUpdated = beforeLastUpdated
                },
                after = new
                {
                    count = jwksCache.Count,
                    lastUpdated = jwksCache.LastUpdated,
                    keys = jwksCache.GetAllKeys().Select(k => new
                    {
                        keyId = k.Key,
                        keyType = k.Value.GetType().Name
                    }).ToList()
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "Failed to refresh JWKS cache", message = ex.Message });
        }
    }
    
    // Helper Methods
    private string? GetAccessTokenFromHeader()
    {
        var authHeader = Request.Headers.Authorization.FirstOrDefault();
        if (authHeader != null && authHeader.StartsWith("Bearer "))
        {
            return authHeader["Bearer ".Length..].Trim();
        }
        return null;
    }

    private ObjectResult CreateErrorResponse(int statusCode, string error, string message, string? details = null)
    {
        var response = new
        {
            error,
            message,
            details = details ?? (HttpContext.RequestServices.GetService<IWebHostEnvironment>()?.IsDevelopment() == true ? details : null),
            timestamp = DateTime.UtcNow,
            path = Request.Path.Value
        };

        return StatusCode(statusCode, response);
    }


    private IActionResult HandleAuthenticationError(Exception ex, string operation)
    {
        return ex switch
        {
            UnauthorizedAccessException => CreateErrorResponse(401, "Unauthorized", $"Authentication failed for {operation}", ex.Message),
            SecurityTokenExpiredException => CreateErrorResponse(401, "Token Expired", "The provided token has expired", ex.Message),
            SecurityTokenInvalidSignatureException => CreateErrorResponse(401, "Invalid Token", "The token signature is invalid", ex.Message),
            SecurityTokenValidationException => CreateErrorResponse(401, "Token Validation Failed", "The provided token is invalid", ex.Message),
            HttpRequestException httpEx when httpEx.Message.Contains("401") => CreateErrorResponse(401, "Authentication Failed", $"Supabase authentication failed for {operation}", httpEx.Message),
            HttpRequestException httpEx when httpEx.Message.Contains("403") => CreateErrorResponse(403, "Forbidden", $"Access denied for {operation}", httpEx.Message),
            HttpRequestException httpEx when httpEx.Message.Contains("429") => CreateErrorResponse(429, "Rate Limited", "Too many requests", httpEx.Message),
            HttpRequestException httpEx => CreateErrorResponse(400, "Bad Request", $"Request failed for {operation}", httpEx.Message),
            TimeoutException => CreateErrorResponse(408, "Request Timeout", $"Request timed out for {operation}", ex.Message),
            InvalidOperationException => CreateErrorResponse(400, "Invalid Operation", ex.Message),
            ArgumentException => CreateErrorResponse(400, "Invalid Argument", ex.Message),
            _ => CreateErrorResponse(500, "Internal Server Error", $"An unexpected error occurred during {operation}", ex.Message)
        };
    }
}

// Additional request models
public class LinkBrokerRequest
{
    public string BrokerId { get; set; } = string.Empty;
}