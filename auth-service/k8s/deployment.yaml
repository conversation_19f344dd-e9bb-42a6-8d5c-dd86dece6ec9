apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: abraapi
  labels:
    app: auth-service
    service: auth
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
        service: auth
        version: v1
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: auth-service
        image: ghcr.io/arnyfesto1/abraapp-auth-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: auth-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: auth-service-config
              key: ASPNETCORE_URLS
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_URL
        - name: SUPABASE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_PROJECT_ID
        - name: SUPABASE_SERVICE_ROLE_KEY
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_SERVICE_ROLE_KEY
        - name: SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_ANON_KEY
        - name: SUPABASE_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_JWT_SECRET
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "100m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      imagePullSecrets:
        - name: ghcr-creds
